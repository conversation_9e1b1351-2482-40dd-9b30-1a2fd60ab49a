import java.util.Properties
import kotlin.collections.*

plugins {
    alias(libs.plugins.autojs.android.application)
}

// 查找签名配置文件，支持多个可能的位置
val possiblePropFiles = listOf(
    File("sign.properties"),
    File("inrt/sign.properties"),
    File("keystore/sign.properties"),
    File(System.getProperty("user.home"), ".android/sign.properties"),
    File("E:/资料/jks/autojs-inrt/sign.properties") // 保留原路径作为备选
)

val propFile: File? = possiblePropFiles.firstOrNull { it.exists() }
val properties = Properties()
if (propFile?.exists() == true) {
    propFile.reader().use {
        properties.load(it)
    }
}

android {
    defaultConfig {
        applicationId = "org.autojs.autoxjs.inrt"
        versionCode = AndroidConfigConventions.VERSION_CODE
        versionName = AndroidConfigConventions.VERSION_NAME
//        multiDexEnabled = true
//        buildConfigField("boolean","isMarket","true") // 这是有注册码的版本
        buildConfigField("boolean", "isMarket", "false")
        javaCompileOptions {
            annotationProcessorOptions {
                arguments["resourcePackageName"] = applicationId.toString()
                arguments["androidManifestFile"] = "$projectDir/src/main/AndroidManifest.xml"
            }
        }
    }
    lint {
        abortOnError = false
        disable += "MissingTranslation"
        disable += "ExtraTranslation"
    }

    // Fix for Android R+ (API 30+) requirement: resources.arsc must be uncompressed and aligned
    androidResources {
        noCompress += ".arsc"
    }

    signingConfigs {
        if (propFile?.exists() == true) {
            getByName("release") {
                storeFile = file(properties.getProperty("storeFile"))
                storePassword = properties.getProperty("storePassword")
                keyAlias = properties.getProperty("keyAlias")
                keyPassword = properties.getProperty("keyPassword")
            }
        }
    }
    buildTypes {
        named("debug") {
            isMinifyEnabled = false
            setProguardFiles(
                listOf(
                    getDefaultProguardFile("proguard-android.txt"),
                    "proguard-rules.pro"
                )
            )
            if (propFile?.exists() == true) {
                signingConfig = signingConfigs.getByName("release")
            }
        }
        named("release") {
            isMinifyEnabled = false
            setProguardFiles(
                listOf(
                    getDefaultProguardFile("proguard-android.txt"),
                    "proguard-rules.pro"
                )
            )
            if (propFile?.exists() == true) {
                signingConfig = signingConfigs.getByName("release")
            }
        }
    }
    flavorDimensions.apply {
        add("channel")
    }

    productFlavors {
        create("common") {
            buildConfigField("boolean", "isMarket", "false")
            manifestPlaceholders.putAll(mapOf("appName" to "inrt"))
            ndk.abiFilters.addAll(listOf("armeabi-v7a", "arm64-v8a"))
        }
        create("template") {
            manifestPlaceholders.putAll(mapOf("appName" to "template"))
            packagingOptions.apply {
                jniLibs.excludes.add("*")
            }
            ndk.abiFilters.add("")
        }
    }
    sourceSets {
        named("main") {
            jniLibs.srcDir("/libs")
            res.srcDirs("src/main/res", "src/main/res-i18n")
        }
    }
    packaging {
        jniLibs {
            useLegacyPackaging = true
        }
        jniLibs.pickFirsts.addAll(
            listOf(
                "lib/arm64-v8a/libc++_shared.so",
                "lib/arm64-v8a/libhiai.so",
                "lib/arm64-v8a/libhiai_ir.so",
                "lib/arm64-v8a/libhiai_ir_build.so",
                "lib/arm64-v8a/libNative.so",
                "lib/arm64-v8a/libpaddle_light_api(_shared.so",
                "lib/armeabi-v7a/libc++_shared.so",
                "lib/armeabi-v7a/libhiai.so",
                "lib/armeabi-v7a/libhiai_ir.so",
                "lib/armeabi-v7a/libhiai_ir_build.so",
                "lib/armeabi-v7a/libNative.so",
                "lib/armeabi-v7a/libpaddle_light_api(_shared.so"
            )
        )
    }
    namespace = "org.autojs.autoxjs.inrt"
}

android.applicationVariants.all {
    val variant = this
    if (variant.flavorName == "template") {
        mergeAssetsProvider.configure {
            doLast {
                delete(
                    fileTree(outputDir) {
                        include(
                            "models/**/*",
                            "mlkit-google-ocr-models/**/*",
                            "project/**/*"
                        )
                    }
                )
            }
        }
    }

}


tasks.register("cp2APP") {
}

tasks.register("cp2APPDebug") {
}


dependencies {
    implementation(projects.automator)
    implementation(projects.common)
    implementation(projects.autojs)

    implementation(libs.androidx.activity.ktx)
    implementation(libs.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.annotation)
    implementation(libs.androidx.preference.ktx)
    implementation(libs.androidx.constraintlayout)

    implementation(libs.androidx.lifecycle.livedata.ktx)
    implementation(libs.androidx.lifecycle.viewmodel.ktx)

    androidTestImplementation(libs.espresso.core)
    implementation(libs.glide) {
        exclude(group = "com.android.support")
    }

    implementation(libs.keeplive)
    implementation(libs.websocket2)
    implementation(libs.utils.everywhere)
    testImplementation(libs.junit)
    implementation(libs.androidx.multidex)
}